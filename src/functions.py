import numpy as np
import pandas as pd
import warnings
import warnings
warnings.filterwarnings('ignore')

def edge_rolling(data: pd.DataFrame, window: int, sign: bool = False, **kwargs) -> pd.Series:
    """
    Compute rolling bid-ask spread estimates from OHLC prices.

    This function uses a rolling window to estimate the bid-ask spread as described in
    <PERSON>rdia, <PERSON>, & <PERSON> (2024). A value of 0.01 means a 1% spread.

    Parameters:
        data : pd.DataFrame
            DataFrame with columns 'open', 'high', 'low', and 'close' (case-insensitive).
        window : int or valid rolling window parameter
            Size of the moving window.
        sign : bool, default False
            If True, the returned spread keeps its sign.
        kwargs : dict
            Extra parameters for the pandas rolling function.

    Returns:
        pd.Series: Rolling spread estimates.
    """
    # Standardize column names and take log-prices.
    df = data.rename(columns=str.lower, inplace=False)
    log_open  = np.log(df['Open'])
    log_high  = np.log(df['High'])
    log_low   = np.log(df['Low'])
    log_close = np.log(df['Close'])
    log_mid   = (log_high + log_low) / 2.0

    # Create lagged series for previous period prices.
    log_high_prev  = log_high.shift(1)
    log_low_prev   = log_low.shift(1)
    log_close_prev = log_close.shift(1)
    log_mid_prev   = log_mid.shift(1)

    # Compute various log-returns.
    r1 = log_mid - log_open        # mid - open
    r2 = log_open - log_mid_prev     # open - previous mid
    r3 = log_mid - log_close_prev    # mid - previous close
    r4 = log_close_prev - log_mid_prev  # previous close - previous mid
    r5 = log_open - log_close_prev   # open - previous close

    # Create an indicator for non-flat periods.
    tau = np.where(
        np.isnan(log_high) | np.isnan(log_low) | np.isnan(log_close_prev),
        np.nan,
        (log_high != log_low) | (log_low != log_close_prev)
    )
    # Indicators to check if open or previous close differ from high/low.
    ind_o_h = tau * np.where(np.isnan(log_open) | np.isnan(log_high), np.nan, log_open != log_high)
    ind_o_l = tau * np.where(np.isnan(log_open) | np.isnan(log_low), np.nan, log_open != log_low)
    ind_c_h = tau * np.where(np.isnan(log_close_prev) | np.isnan(log_high_prev), np.nan, log_close_prev != log_high_prev)
    ind_c_l = tau * np.where(np.isnan(log_close_prev) | np.isnan(log_low_prev), np.nan, log_close_prev != log_low_prev)

    # Calculate products of returns that will be used in moment conditions.
    prod_12 = r1 * r2
    prod_34 = r3 * r4
    prod_15 = r1 * r5
    prod_45 = r4 * r5
    tau_r1  = tau * r1
    tau_r2  = tau * r2
    tau_r4  = tau * r4
    tau_r5  = tau * r5

    # Collect all intermediate values in a DataFrame.
    vals = pd.DataFrame({
        'prod_12': prod_12,
        'prod_34': prod_34,
        'prod_15': prod_15,
        'prod_45': prod_45,
        'tau': tau,
        'r1': r1,
        'tau_r2': tau_r2,
        'r3': r3,
        'tau_r4': tau_r4,
        'r5': r5,
        'prod_12_sq': prod_12 ** 2,
        'prod_34_sq': prod_34 ** 2,
        'prod_15_sq': prod_15 ** 2,
        'prod_45_sq': prod_45 ** 2,
        'prod_12_34': prod_12 * prod_34,
        'prod_15_45': prod_15 * prod_45,
        'tau_r2_r2': tau_r2 * r2,
        'tau_r4_r4': tau_r4 * r4,
        'tau_r5_r5': tau_r5 * r5,
        'tau_r2_prod12': tau_r2 * prod_12,
        'tau_r4_prod34': tau_r4 * prod_34,
        'tau_r5_prod15': tau_r5 * prod_15,
        'tau_r4_prod45': tau_r4 * prod_45,
        'tau_r4_prod12': tau_r4 * prod_12,
        'tau_r2_prod34': tau_r2 * prod_34,
        'tau_r2_r4': tau_r2 * r4,
        'tau_r1_prod45': tau_r1 * prod_45,
        'tau_r5_prod45': tau_r5 * prod_45,
        'tau_r4_r5': tau_r4 * r5,
        'tau_r5_only': tau_r5,
        'ind_o_h': ind_o_h,
        'ind_o_l': ind_o_l,
        'ind_c_h': ind_c_h,
        'ind_c_l': ind_c_l
    }, index=df.index)

    # The first observation is not usable (due to shifting).
    vals.iloc[0] = np.nan

    # Adjust window length and min_periods to account for the lag.
    window_adj = window - 1 if isinstance(window, (int, np.integer)) else window
    if 'min_periods' in kwargs and isinstance(kwargs['min_periods'], (int, np.integer)):
        kwargs['min_periods'] = max(0, kwargs['min_periods'] - 1)

    # Compute rolling means for each column.
    roll_vals = vals.rolling(window=window_adj, **kwargs).mean()

    # Calculate probabilities needed for the estimator.
    p_tau = roll_vals['tau']
    p_open = roll_vals['ind_o_h'] + roll_vals['ind_o_l']
    p_close = roll_vals['ind_c_h'] + roll_vals['ind_c_l']

    # Count valid tau observations.
    count_tau = vals['tau'].rolling(window=window_adj, **kwargs).sum()
    # Mark window as missing if there are fewer than 2 valid periods or zero probabilities.
    roll_vals[(count_tau < 2) | (p_open == 0) | (p_close == 0)] = np.nan

    # Compute coefficients from the rolling means.
    a1 = -4.0 / p_open
    a2 = -4.0 / p_close
    a3 = roll_vals['r1'] / p_tau
    a4 = roll_vals['tau_r4'] / p_tau
    a5 = roll_vals['r3'] / p_tau
    a6 = roll_vals['r5'] / p_tau

    a12 = 2 * a1 * a2
    a11 = a1 ** 2
    a22 = a2 ** 2
    a33 = a3 ** 2
    a55 = a5 ** 2
    a66 = a6 ** 2

    # Compute expectations from moment conditions.
    E1 = a1 * (roll_vals['prod_12'] - a3 * roll_vals['tau_r2']) + \
         a2 * (roll_vals['prod_34'] - a4 * roll_vals['r3'])
    E2 = a1 * (roll_vals['prod_15'] - a3 * roll_vals['tau_r5_only']) + \
         a2 * (roll_vals['prod_45'] - a4 * roll_vals['r5'])

    # Compute variances from the moments.
    V1 = - E1**2 + (
        a11 * (roll_vals['prod_12_sq'] - 2 * a3 * roll_vals['tau_r2_prod12'] + a33 * roll_vals['tau_r2_r2']) +
        a22 * (roll_vals['prod_34_sq'] - 2 * a5 * roll_vals['tau_r4_prod34'] + a55 * roll_vals['tau_r4_r4']) +
        a12 * (roll_vals['prod_12_34'] - a3 * roll_vals['tau_r2_prod34'] - a5 * roll_vals['tau_r4_prod12'] + a3 * a5 * roll_vals['tau_r2_r4'])
    )
    V2 = - E2**2 + (
        a11 * (roll_vals['prod_15_sq'] - 2 * a3 * roll_vals['tau_r5_prod15'] + a33 * roll_vals['tau_r5_r5']) +
        a22 * (roll_vals['prod_45_sq'] - 2 * a6 * roll_vals['tau_r4_prod45'] + a66 * roll_vals['tau_r4_r4']) +
        a12 * (roll_vals['prod_15_45'] - a3 * roll_vals['tau_r5_prod45'] - a6 * roll_vals['tau_r4_r5'] + a3 * a6 * roll_vals['tau_r4_r5'])
    )

    tot_var = V1 + V2
    # If variance is positive, use a weighted combination; otherwise, take a simple average.
    s2 = np.where(tot_var > 0, (V2 * E1 + V1 * E2) / tot_var, (E1 + E2) / 2.0)
    spread = np.sqrt(np.abs(s2))
    if sign:
        spread *= np.sign(s2)

    return spread