#!/usr/bin/env python3
"""
Teste da função edge_rolling
"""

import sys
sys.path.append('src')
import yfinance as yf
import pandas as pd
from functions import edge_rolling

def test_edge_rolling():
    """Testa a função edge_rolling com dados reais"""
    
    # Testar com uma ação
    ticker = 'PETR4.SA'
    print(f"Testando função edge_rolling com {ticker}...")
    
    try:
        stock = yf.Ticker(ticker)
        dados = stock.history(period='3mo')
        
        if dados.empty:
            print("❌ Não foi possível obter dados")
            return False
            
        print(f"✅ Dados obtidos: {len(dados)} dias")
        print(f"📋 Colunas disponíveis: {list(dados.columns)}")

        # Testar a função edge_rolling
        spread = edge_rolling(dados[['Open', 'High', 'Low', 'Close']], window=20)
        spread_pct = spread * 100
        
        print("✅ Função edge_rolling executada com sucesso")
        print(f"📊 Spread médio: {spread_pct.mean():.4f}%")
        print(f"📊 Spread min/max: {spread_pct.min():.4f}% / {spread_pct.max():.4f}%")
        print(f"📊 Valores não-nulos: {spread_pct.count()}/{len(spread_pct)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    test_edge_rolling()
